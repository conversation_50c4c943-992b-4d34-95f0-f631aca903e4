<a id="camel.toolkits.openai_agent_toolkit"></a>

<a id="camel.toolkits.openai_agent_toolkit.OpenAIAgentToolkit"></a>

## OpenAIAgentToolkit

```python
class OpenAIAgentToolkit(BaseToolkit):
```

Toolkit for accessing OpenAI's agent tools including web search and
file search.

Provides access to OpenAI's web search and file search capabilities
through the Responses API, allowing agents to retrieve information from
the web and search through uploaded files.

<a id="camel.toolkits.openai_agent_toolkit.OpenAIAgentToolkit.__init__"></a>

### __init__

```python
def __init__(
    self,
    model: Optional[BaseModelBackend] = None,
    api_key: Optional[str] = None,
    timeout: Optional[float] = None
):
```

Initialize the OpenAI agent toolkit.

**Parameters:**

- **model** (BaseModelBackend): The OpenAI model to use for responses. If None, defaults to gpt-4o-mini. (default: :obj:`None`)
- **api_key** (str): OpenAI API key. If not provided, will attempt to use OPENAI_API_KEY environment variable. (default: :obj:`None`)
- **timeout** (Optional[float]): The timeout value for API requests in seconds. If None, no timeout is applied. (default: :obj:`None`)

<a id="camel.toolkits.openai_agent_toolkit.OpenAIAgentToolkit.web_search"></a>

### web_search

```python
def web_search(self, query: str):
```

Perform a web search using OpenAI's web search tool.

**Parameters:**

- **query** (str): The search query.

**Returns:**

  str: The search result or error message.

<a id="camel.toolkits.openai_agent_toolkit.OpenAIAgentToolkit.file_search"></a>

### file_search

```python
def file_search(self, query: str, vector_store_id: str):
```

Search through files using OpenAI's file search tool.

**Parameters:**

- **query** (str): The search query.
- **vector_store_id** (str): The vector store ID to search in.

**Returns:**

  str: The search result or error message.

<a id="camel.toolkits.openai_agent_toolkit.OpenAIAgentToolkit.get_tools"></a>

### get_tools

```python
def get_tools(self):
```

**Returns:**

  List[FunctionTool]: Collection of FunctionTool objects representing
the available search functions in this toolkit.
